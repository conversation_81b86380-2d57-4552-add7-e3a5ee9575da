"use client";
import Image from "next/image";
import Link from "next/link";

type ImageData = {
  id: number;
  images: string[];
  index: number;
};

interface AstriaPacksCardProps {
  imageData: ImageData;
  packId?: number;
}

export default function AstriaPacksCard({ imageData, packId }: AstriaPacksCardProps) {
  return (
    <Link
      href={`/packs/${packId}/${imageData.id}`}
      key={imageData.id}
      className={`relative ${(imageData.index as number) % 2 === 0 ? "mt-16" : "mt-0"}`}
    >
      <div className="aspect-[3/4] w-full overflow-hidden rounded-lg">
        <div className="w-full h-full relative">
          <Image
            src={imageData.images?.[0] ?? ""}
            alt={`Generated image from prompt ${imageData.id}`}
            fill
            className="object-cover transition-transform duration-500 ease-in-out hover:scale-105"
            sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 20vw"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent opacity-70" />
        </div>
      </div>
    </Link>
  );
}
