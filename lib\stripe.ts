import { Stripe, loadStripe } from "@stripe/stripe-js";
import { db } from "@/lib/firebase";
import {
  doc,
  collection,
  query,
  where,
  getDocs,
  deleteDoc,
  addDoc,
  onSnapshot,
} from "firebase/firestore";
import { User, getAuth } from "firebase/auth";
import { useEffect, useState } from "react";
import { PLANS } from "./constants";
let stripePromise: Stripe | null;

const initializeStripe = async () => {
  if (!stripePromise) {
    stripePromise = await loadStripe(
      String(process.env.NEXT_PUBLIC_STRIPE_API_KEY_TEST)
    );
  }
  return stripePromise;
};

// Create a checkout session without storing subscription details immediately.
// The success_url now includes the priceId so we can later look up the plan.
export async function createCheckoutSession(userId: string, priceId: string) {
  const checkoutSessionRef = await addDoc(
    collection(doc(db, "users", userId), "checkout_sessions"),
    {
      price: priceId,
      success_url:
        window.location.origin + `/payment-success?priceId=${priceId}`,
      cancel_url: window.location.origin,
      createdAt: new Date(),
    }
  );

  // Listen for the sessionId update and then redirect to Stripe checkout.
  onSnapshot(checkoutSessionRef, async (snap) => {
    if (snap.exists()) {
      const data = snap.data();

      const { sessionId } = data;
      if (sessionId) {
        const stripe = await initializeStripe();
        stripe?.redirectToCheckout({ sessionId });
      } else {
        console.log("Session ID is missing");
      }
    } else {
      console.log("Error: Document does not exist");
    }
  });
}

// This function finds the matching plan (from your PLANS constant),
// deletes any existing subscription records, and stores the new subscription details.
export async function confirmSubscription(userId: string, priceId: string) {
  const selectedPlan = PLANS.find(
    (plan) => plan.priceId === priceId || plan.yearlyPriceId === priceId
  );

  if (!selectedPlan) {
    console.error("No matching plan found for priceId:", priceId);
    return;
  }

  // Reference to the user's subscribedPlan collection.
  const subscribedPlanRef = collection(
    doc(db, "users", userId),
    "subscribedPlan"
  );

  // Delete any existing subscription.
  const existingSubscriptions = await getDocs(subscribedPlanRef);
  existingSubscriptions.forEach(async (docSnapshot) => {
    await deleteDoc(docSnapshot.ref);
  });

  // Save new plan details in Firestore under 'subscribedPlan'.
  await addDoc(subscribedPlanRef, {
    planName: selectedPlan.name,
    planPrice: selectedPlan.price,
    planCredits: selectedPlan.credit,
    planImageCredits: selectedPlan.imageCredits,
    features: [
      selectedPlan.feature1,
      selectedPlan.feature2,
      selectedPlan.feature3,
      selectedPlan.feature4,
      selectedPlan.feature5,
    ],
    subscribedAt: new Date(),
  });
  return { success: true };
}

export async function isUserPremium(): Promise<boolean> {
  const auth = getAuth(); // Get the auth instance

  // Ensure user is authenticated
  const user = auth.currentUser;
  if (!user) return false;

  // Refresh the ID token to get the latest claims
  await user.getIdToken(true);

  // Fetch the decoded token containing claims
  const decodedToken = await user.getIdTokenResult();

  // Check if the user has the premium role
  return decodedToken?.claims?.stripeRole ? true : false;
}

export async function getUserStripeRole(): Promise<Object | null> {
  const auth = getAuth(); // Get the Firebase Auth instance
  const user = auth.currentUser;

  if (!user) {
    console.log("No authenticated user found.");
    return null;
  }

  // Refresh the token to ensure claims are up-to-date
  await user.getIdToken(true);

  // Retrieve decoded token claims
  const decodedToken = await user.getIdTokenResult();

  // Extract the stripeRole claim
  const stripeRole = decodedToken?.claims?.stripeRole || null;

  return stripeRole;
}

export function usePremiumStatus(user: any) {
  const [premiumStatus, setPremiumStatus] = useState<boolean>(false);

  useEffect(() => {
    async function checkPremiumStatus() {
      // Only check if user exists; otherwise, do nothing
      if (user) {
        const result = await isUserPremium();
        setPremiumStatus(result);
      } else {
        // Optionally, reset the premium status if no user exists
        setPremiumStatus(false);
      }
    }
    checkPremiumStatus();
  }, [user]);

  return premiumStatus;
}

/**
 * @param packClass Type of the pack e.g man, women, cat etc.
 * @param userId Firebase userId.
 * @param packId Astria Ai packId.
 * @param price Numerical price of the pack (e.g., 5.92).
 * @param productName Name of the product to display on Stripe Checkout.
 * @description This Function will create checkout session for buying packs with a dynamic price.
 */
export  async function packPaymentCheckoutSessions(
  packClass: string,
  userId: string,
  packId: string | number,
  price: number,
  productName: string,
  images: string[]
) {
  if (!userId || !packClass || !packId || !price || !productName) {
    return { success: false, message: "Missing required parameters" };
  }

  const lineItemData = {
    price_data: {
      currency: "usd",
      unit_amount: Math.round(price * 100),
      product_data: {
        name: productName,
        images,
      },
    },
    quantity: 1,
  };

  const checkoutSessionRef = await addDoc(
    collection(db, "users", userId, "checkout_sessions"),
    {
      line_items: [lineItemData],
      mode: "payment",
      success_url:
        window.location.origin +
        `/packs/payment-success?packClass=${packClass}&packId=${packId}`,
      cancel_url: window.location.origin,
      createdAt: new Date(),
      metadata: {
        userId,
        packClass,
        packId,
      },
    }
  );

  onSnapshot(checkoutSessionRef, async (snap) => {
    if (snap.exists()) {
      const data = snap.data();
      const { sessionId, url } = data;
      console.log("Session Data:", data);

      if (sessionId) {
        const stripe = await initializeStripe();
        if (stripe) {
          stripe.redirectToCheckout({ sessionId });
        } else {
          console.error("Stripe not initialized.");
        }
      } else if (url) {
        window.location.href = url;
      } else {
        console.error(
          "Session ID or URL is missing in the snapshot data. Full data:",
          data
        );
      }
    } else {
      console.warn(
        "Checkout session document no longer exists for session ID:",
        checkoutSessionRef.id
      );
    }
  });
}
