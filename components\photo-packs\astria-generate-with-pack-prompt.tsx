"use client";
import { PromptData } from "@/types/astria";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useEffect, useState } from "react";
import { Camera, InfoIcon } from "lucide-react";
import Image from "next/image";
import { Slider } from "../ui/slider";
import { CustomToolTipWrapper } from "../CustomToolTipWrapper";
import { Button } from "../ui/Button";
import { useAuthState } from "react-firebase-hooks/auth";
import { auth } from "@/lib/firebase";
import { collection, query, where, getDocs } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { ExsitigModelData } from "@/types/model";
import { useToast } from "@/hooks/use-toast";
import { createAstriaPackPromptFormData } from "@/lib/astria-packs-prompt-utils";
import DisplayImage from "./DisplayImage";
import AuthRequiredScreen from "./AuthRequiredScreen";
import AuthLoadingScreen from "../model/AuthLoadingScreen";

interface AstriaGenerateWithPackPromtProps {
  promtData: PromptData;
}

export function AstriaGenerateWithPackPromt({
  promtData,
}: AstriaGenerateWithPackPromtProps) {
  const [user, loadingUser] = useAuthState(auth);
  const [image, setImage] = useState<File | null>(null);
  const [numImages, setNumImages] = useState(1);
  const [guidanceScale, setGuidanceScale] = useState(0.5);
  const [models, setModels] = useState<ExsitigModelData[] | null>();
  const [selectedModel, setSelectedModel] = useState<string | undefined>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [images, setImages] = useState<string[]>([]);

  const existingMoels = async () => {
    let models: ExsitigModelData[] = [];
    const q = query(collection(db, "models"), where("userId", "==", user?.uid));
    const snapShot = await getDocs(q);
    snapShot.forEach((doc) => {
      const data = doc.data();
      models.push(data as ExsitigModelData);
    });
    setModels(models);
  };

  useEffect(() => {
    if (user && user.uid) {
      existingMoels();
    }
  }, [user]);

  const cleanPrompt = cleanPromptText(promtData.text);
  const { toast } = useToast();

  const generateImages = async () => {
    if (image && selectedModel) {
      toast({
        title: "Invalid Selection",
        description: "Please select either an image OR a model, not both.",
      });
      return;
    }
    if (!image && !selectedModel) {
      toast({
        title: "Missing Input",
        description:
          "Please select either an image or a model to generate images.",
        variant: "destructive",
      });
      return;
    }

    const formData = createAstriaPackPromptFormData({
      text: promtData.text || "",
      modelId: selectedModel || "",
      numImages: numImages.toString(),
      image: image || new File([], ""),
      guidanceScale: guidanceScale.toString(),
      userId: user?.uid,
    });
    setIsLoading(true);
    try {
      const res = await fetch(
        `/api/proxy/generate-with-pack?tuneId=${promtData.tune_id}`,
        {
          method: "POST",
          body: formData,
        }
      );
      const data = await res.json();
      const promptId = data.response.id
      await checkPromptResult(promptId);
    } catch (error) {
      console.error("Error generating images:", error);
      toast({
        title: "Error",
        description: "An error occurred while generating images.",
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };



  const checkPromptResult = async (promptId: number) => {
    if (!promptId) return;
    while (true) {
      const res = await fetch(`/api/proxy/prompt-result/${promptId}`);
      const data = await res.json();
      console.log(data);
      if (data.images && data.images.length > 0) {
        setImages(data.images);
        setIsLoading(false);
        break;
      }
      await new Promise((resolve) => setTimeout(resolve, 5000));
    }
  };


  if (loadingUser) return <AuthLoadingScreen />;

  if (!user) return <AuthRequiredScreen />;

  return (
    <div className="pt-20 min-h-screen flex items-center justify-center">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 h-full max-w-7xl mx-auto px-4">
        {/* Left Column - Controls */}
        <div className="flex flex-col gap-4 border p-6 rounded-lg ">
          {/* Prompt Text */}
          <div className="bg-gray-700/50 rounded-lg p-4 h-40 overflow-auto">
            <p className="text-gray-200 text-sm">{cleanPrompt}</p>
          </div>

          {/* Model Selection */}
          <div className="bg-gray-700/50   rounded-lg p-4 relative">
            <div className="flex justify-between items-center mb-2">
              <span className="text-gray-300 text-sm">Model</span>
              <CustomToolTipWrapper content="Please select a valide model that matches the prompt.">
                <InfoIcon className="h-4 w-4 text-gray-400" />
              </CustomToolTipWrapper>
            </div>
            <Select
              value={selectedModel}
              onValueChange={(value) => setSelectedModel(value)}
            >
              <SelectTrigger className="w-full bg-gray-800 border-gray-700">
                <SelectValue placeholder="Select a model" />
              </SelectTrigger>
              <SelectContent>
                {models && models.length > 0 ? (
                  models.map((model, idx) => (
                    <SelectItem key={idx} value={model.tuneId}>
                      {model.title}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-models" disabled>
                    No models available
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Image Upload */}
          <div className="bg-gray-700/50 rounded-lg p-4">
            <button
              onClick={() => document.getElementById("imageInput")?.click()}
              className="w-full flex items-center justify-center gap-2 py-3 bg-gray-800 hover:bg-gray-700/50 text-gray-300 rounded-lg border border-gray-600"
            >
              <Camera className="h-4 w-4" />
              <span>Browse or Upload Image</span>
            </button>
            <input
              type="file"
              onChange={(e) => setImage(e.target.files?.[0] ?? null)}
              id="imageInput"
              className="hidden"
              accept="image/*"
            />
            {image && (
              <div className="mt-4 relative h-40 w-full">
                <Image
                  src={URL.createObjectURL(image)}
                  alt="Selected"
                  fill
                  className="object-contain rounded-lg"
                />
              </div>
            )}
          </div>

          {/* Guidance Scale */}
          <div className="bg-gray-700/50 rounded-lg p-4">
            <div className="mb-2 flex justify-between items-center">
              <span className="text-gray-300 text-sm">Guidance Scale</span>
              <span className="text-gray-400 text-sm">
                {guidanceScale.toFixed(1)}
              </span>
            </div>
            <Slider
              defaultValue={[guidanceScale]}
              min={0}
              max={1}
              step={0.1}
              onValueChange={(value) => setGuidanceScale(value[0])}
              className="my-4"
            />
          </div>
          {/* Number of Images */}
          <div className="bg-gray-700/50 rounded-lg p-4">
            <div className="mb-2 flex justify-between items-center">
              <span className="text-gray-300 text-sm">Number of Images</span>
              <span className="text-gray-400 text-sm">{numImages}</span>
            </div>
            <Slider
              defaultValue={[numImages]}
              min={1}
              max={8}
              step={1}
              onValueChange={(value) => setNumImages(value[0])}
              className="my-4"
            />
          </div>

          {/* Generate Button */}
          <Button
            onClick={generateImages}
            className="w-full bg-gradient-to-r from-purple-600 to-pink-500 hover:from-purple-500 hover:to-pink-400 !text-white py-3 rounded-lg transition-all duration-300"
            disabled={isLoading}
          >
            Generate
          </Button>
        </div>

        {/* Right Column - Results */}
        <div className="col-span-2  border rounded-lg p-6 ">
          <DisplayImage loading={isLoading} images={images} />
        </div>
      </div>
    </div>
  );
}

function cleanPromptText(text: string | undefined): string {
  if (!text) return "Prompt not available";
  return text.replace(/<(faceid|concept):[^>]+>/gi, "").trim();
}
