// lib/astria-form-utils.ts
// Server-side utilities for creating Astria API FormData objects

export interface AstriaPromptParams {
  prompt: string;
  user: string;
  tuneId: string;
  enhancementType?: string;
  inputImage?: File | null;
  controlnet?: string | null;
  denoisingStrength?: string | null;
  style?: string | null;
  garmentModelId?: string | null;
  controlnets?: string[];
  controlnetWeights?: string[];
  maskPrompt?: string;
  maskInvert?: string;
  superResolution?: string;
  hiresFixEnabled?: string;
  tiledUpscale?: string;
  tiledUpscaleVersion?: string;
  hiresCfgScale?: string;
  resemblance?: string;
  backendVersion?: string;
}

export interface AstriaVideoParams {
  prompt: string;
  user: string;
  tuneId: string;
  videoPrompt?: string | null;
  videoModel?: string | null;
  inputImage?: File | null;
  controlnet?: string | null;
  denoisingStrength?: string | null;
  style?: string | null;
  backendVersion?: string;
}

export interface AstriaTuneParams {
  title: string;
  user: string;
  images: File[];
  modelType?: string;
  enhancementType?: string;
  className?: string;
  branch?: string;
  preset?: string;
}

export interface AstriaAvatarParams {
  prompt: string;
  user: string;
  modelId: string;
  inputImage?: File | null;
  controlnet?: string | null;
  denoisingStrength?: string | null;
  style?: string | null;
  backendVersion?: string;
}

export interface AstriaUpscaleParams {
  prompt: string;
  user: string;
  modelId: string;
  inputImage: File;
  denoisingStrength?: string;
  superResolution?: string;
  hiresFixEnabled?: string;
  hiresCfgScale?: string | null;
  resemblance?: string | null;
  backendVersion?: string;
}

/**
 * Creates FormData for Astria prompt/image generation API
 * Centralizes the server-side FormData creation for Astria API calls
 */
export function createAstriaPromptFormData(params: AstriaPromptParams): FormData {
  const astriaFormData = new FormData();

  // Build the prompt text with user token
  const promptText = `<lora:${params.tuneId}:1.0> sks ${params.user} ${params.prompt}`;
  astriaFormData.append("prompt[text]", promptText);

  // Set number of images to 1
  astriaFormData.append("prompt[num_images]", "1");

  // Handle image-to-image generation if input image is provided
  if (params.inputImage) {
    astriaFormData.append("prompt[input_image]", params.inputImage, params.inputImage.name);

    // Add controlnet type if provided
    if (params.controlnet) {
      astriaFormData.append("prompt[controlnet]", params.controlnet);
    }

    // Add denoising strength if provided (default to 0.7 if not specified)
    astriaFormData.append(
      "prompt[denoising_strength]",
      params.denoisingStrength || "0.7"
    );

    // Set controlnet_txt2img to false for image-to-image generation
    astriaFormData.append("prompt[controlnet_txt2img]", "false");
  }

  // Add style if provided
  if (params.style) {
    astriaFormData.append("prompt[style]", params.style);
  }

  // Add garment model ID if provided (for virtual try-on)
  if (params.garmentModelId) {
    astriaFormData.append("prompt[garment_model_id]", params.garmentModelId);
  }

  // Add interior design specific parameters
  if (params.controlnets && params.controlnets.length > 0) {
    params.controlnets.forEach((controlnet, index) => {
      astriaFormData.append(`prompt[controlnets][${index}]`, controlnet);
    });
  }

  if (params.controlnetWeights && params.controlnetWeights.length > 0) {
    params.controlnetWeights.forEach((weight, index) => {
      astriaFormData.append(`prompt[controlnet_weights][${index}]`, weight);
    });
  }

  if (params.maskPrompt) {
    astriaFormData.append("prompt[mask_prompt]", params.maskPrompt);
  }

  if (params.maskInvert) {
    astriaFormData.append("prompt[mask_invert]", params.maskInvert);
  }

  // Add upscaling parameters
  if (params.superResolution) {
    astriaFormData.append("prompt[super_resolution]", params.superResolution);
  }

  if (params.hiresFixEnabled) {
    astriaFormData.append("prompt[hires_fix]", params.hiresFixEnabled);
  }

  if (params.tiledUpscale) {
    astriaFormData.append("prompt[tiled_upscale]", params.tiledUpscale);
  }

  if (params.tiledUpscaleVersion) {
    astriaFormData.append("prompt[tiled_upscale_version]", params.tiledUpscaleVersion);
  }

  if (params.hiresCfgScale) {
    astriaFormData.append("prompt[hires_cfg_scale]", params.hiresCfgScale);
  }

  if (params.resemblance) {
    astriaFormData.append("prompt[resemblance]", params.resemblance);
  }

  // Set backend version
  astriaFormData.append("prompt[backend_version]", params.backendVersion || "0");

  // Add enhancement type
  if (params.enhancementType) {
    astriaFormData.append("prompt[enhancement_type]", params.enhancementType);
  }

  return astriaFormData;
}

/**
 * Creates FormData for Astria video generation API
 */
export function createAstriaVideoFormData(params: AstriaVideoParams): FormData {
  const astriaFormData = new FormData();

  // Build the prompt text with user token
  const promptText = `<lora:${params.tuneId}:1.0> sks ${params.user} ${params.prompt}`;
  astriaFormData.append("prompt[text]", promptText);

  // Set number of images to 1
  astriaFormData.append("prompt[num_images]", "1");

  // Handle image-to-image generation if input image is provided
  if (params.inputImage) {
    astriaFormData.append("prompt[input_image]", params.inputImage, params.inputImage.name);

    // Add controlnet type if provided
    if (params.controlnet) {
      astriaFormData.append("prompt[controlnet]", params.controlnet);
    }

    // Add denoising strength if provided (default to 0.8 for video)
    astriaFormData.append(
      "prompt[denoising_strength]",
      params.denoisingStrength || "0.8"
    );

    // Set controlnet_txt2img to false for image-to-image generation
    astriaFormData.append("prompt[controlnet_txt2img]", "false");
  }

  // Add style if provided
  if (params.style) {
    astriaFormData.append("prompt[style]", params.style);
  }

  // Add video-specific parameters
  if (params.videoPrompt) {
    astriaFormData.append("prompt[video_prompt]", params.videoPrompt);
  }

  if (params.videoModel) {
    astriaFormData.append("prompt[video_model]", params.videoModel);
  }

  // Set backend version
  astriaFormData.append("prompt[backend_version]", params.backendVersion || "0");

  return astriaFormData;
}

/**
 * Creates FormData for Astria tune creation API
 */
export function createAstriaTuneFormData(params: AstriaTuneParams): FormData {
  const astriaFormData = new FormData();

  // Add images
  params.images.forEach((image) => {
    astriaFormData.append('tune[images][]', image, image.name);
  });

  // Add tune metadata
  astriaFormData.append("tune[title]", params.title);
  astriaFormData.append("tune[name]", params.user);
  astriaFormData.append("tune[branch]", params.branch || "flux1");
  astriaFormData.append("tune[model_type]", params.modelType || "lora");

  // Add preset based on model type and enhancement type
  if (params.preset) {
    astriaFormData.append("tune[preset]", params.preset);
  } else if (params.modelType === "lora") {
    astriaFormData.append("tune[preset]", "flux-lora-portrait");
  }

  // Add className for faceid models
  if (params.modelType === "faceid" && params.className) {
    astriaFormData.append("tune[class_name]", params.className);
  }

  return astriaFormData;
}

/**
 * Creates FormData for Astria avatar generation API
 */
export function createAstriaAvatarFormData(params: AstriaAvatarParams): FormData {
  const astriaFormData = new FormData();

  // Add the prompt text (no user token for avatar generation)
  astriaFormData.append("prompt[text]", params.prompt);

  // Set number of images to 1
  astriaFormData.append("prompt[num_images]", "1");

  // Handle image-to-image generation if input image is provided
  if (params.inputImage) {
    astriaFormData.append("prompt[input_image]", params.inputImage, params.inputImage.name);

    // Add controlnet type if provided
    if (params.controlnet) {
      astriaFormData.append("prompt[controlnet]", params.controlnet);
    }

    // Add denoising strength if provided
    if (params.denoisingStrength) {
      astriaFormData.append("prompt[denoising_strength]", params.denoisingStrength);
    }

    // Set controlnet_txt2img to false for image-to-image generation
    astriaFormData.append("prompt[controlnet_txt2img]", "false");
  }

  // Add style if provided
  if (params.style) {
    astriaFormData.append("prompt[style]", params.style);
  }

  // Set backend version
  astriaFormData.append("prompt[backend_version]", params.backendVersion || "0");

  // Add enhancement type
  astriaFormData.append("prompt[enhancement_type]", "avatar");

  return astriaFormData;
}

/**
 * Creates FormData for Astria upscaling API
 */
export function createAstriaUpscaleFormData(params: AstriaUpscaleParams): FormData {
  const astriaFormData = new FormData();

  // Build the prompt text with user token
  const promptText = `<lora:${params.modelId}:1.0> sks ${params.user} ${params.prompt}`;
  astriaFormData.append("prompt[text]", promptText);

  // Set number of images to 1
  astriaFormData.append("prompt[num_images]", "1");

  // Add the input image (required for upscaling)
  astriaFormData.append("prompt[input_image]", params.inputImage, params.inputImage.name);

  // Add denoising strength
  if (params.denoisingStrength) {
    astriaFormData.append("prompt[denoising_strength]", params.denoisingStrength);
  }

  // Add upscaling parameters
  if (params.superResolution) {
    astriaFormData.append("prompt[super_resolution]", params.superResolution);
  }

  if (params.hiresFixEnabled) {
    astriaFormData.append("prompt[hires_fix]", params.hiresFixEnabled);
  }

  if (params.hiresCfgScale) {
    astriaFormData.append("prompt[hires_cfg_scale]", params.hiresCfgScale);
  }

  if (params.resemblance) {
    astriaFormData.append("prompt[resemblance]", params.resemblance);
  }

  // Set backend version
  astriaFormData.append("prompt[backend_version]", params.backendVersion || "0");

  return astriaFormData;
}
