'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/Button';
import { ArrowRight } from 'lucide-react';

interface PackCard {
  id: string;
  title: string;
  description: string;
  imageSrc: string;
  offset?: boolean;
}

const packData: PackCard[] = [
  { 
    id: '1', 
    title: 'Professional Headshots', 
    description: 'Perfect for LinkedIn and professional profiles', 
    imageSrc: '/_gallery/11.jpg',
    offset: false
  },
  { 
    id: '2', 
    title: 'Fashion Model', 
    description: 'Stunning fashion photography styles', 
    imageSrc: '/_gallery/12.jpg',
    offset: true
  },
  { 
    id: '3', 
    title: 'Portrait Photography', 
    description: 'Beautiful portrait styles for any occasion', 
    imageSrc: '/_gallery/13.jpg',
    offset: false
  },
  { 
    id: '4', 
    title: 'Food Photography', 
    description: 'Make your culinary creations look amazing', 
    imageSrc: '/_gallery/4.jpg',
    offset: true
  },
  { 
    id: '5', 
    title: 'Travel Shots', 
    description: 'Adventure and travel themed photography', 
    imageSrc: '/_gallery/5.jpg',
    offset: false
  },
  { 
    id: '6', 
    title: 'Vintage Style', 
    description: 'Classic and timeless photo aesthetics', 
    imageSrc: '/_gallery/6.jpg',
    offset: true
  },
  { 
    id: '7', 
    title: 'Urban Style', 
    description: 'Modern city vibes and urban aesthetics', 
    imageSrc: '/_gallery/7.jpg',
    offset: false
  },
  { 
    id: '8', 
    title: 'Studio Lighting', 
    description: 'Professional studio quality photos', 
    imageSrc: '/_gallery/8.jpg',
    offset: true
  },
  { 
    id: '9', 
    title: 'Outdoor Adventure', 
    description: 'Capture the spirit of outdoor exploration', 
    imageSrc: '/_gallery/9.jpg',
    offset: false
  },
  { 
    id: '10', 
    title: 'Business Casual', 
    description: 'Professional yet approachable business style', 
    imageSrc: '/_gallery/10.jpg',
    offset: true
  },
];

export default function Packs() {
  const [isHovered, setIsHovered] = useState<string | null>(null);

  return (
    <div className="min-h-screen pt-20 pb-16">
      {/* Hero Section */}
      <div className="container mx-auto px-4 mb-16 text-center">
        <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
          Generate from Packs
        </h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Choose from our curated collection of AI photo generation packs to create stunning images in various styles
        </p>
      </div>
      
      {/* Staggered Grid */}
      <div className="p-2 md:py-4 lg:px-20">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3 md:gap-4">
          {packData.map((pack) => (
            <div
              key={pack.id}
              className={`relative ${pack.offset ? "mt-16" : "mt-0"}`}
              onMouseEnter={() => setIsHovered(pack.id)}
              onMouseLeave={() => setIsHovered(null)}
            >
              <div className="aspect-[3/4] w-full overflow-hidden rounded-lg">
                <div className="w-full h-full relative">
                  <Image
                    src={pack.imageSrc}
                    alt={pack.title}
                    fill
                    className="object-cover transition-transform duration-500 ease-in-out hover:scale-105"
                    sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 20vw"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent opacity-70" />
                  
                  <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                    <h3 className="font-bold text-lg">{pack.title}</h3>
                    <p className="text-sm text-white/80 mt-1">{pack.description}</p>
                    
                    {isHovered === pack.id ? (
                      <Button variant="outline" size="sm" className="mt-3 bg-white/10 backdrop-blur-sm border-white/30 hover:bg-white/20">
                        Generate
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    ) : null}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
