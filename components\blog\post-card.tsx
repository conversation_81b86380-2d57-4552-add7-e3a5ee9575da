import Image from "next/image"
import Link from "next/link"
import type { Post } from "@/types/blogs"
import { formatDate } from "@/utility/utility"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>ooter, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface PostCardProps {
  post: Post
}

export default function PostCard({ post }: PostCardProps) {
  return (
    <Card className="overflow-hidden h-full flex flex-col bg-white">
      <Link href={`/blog/${post.slug}`} className="block relative h-48 overflow-hidden">
        <Image
          src={post.coverImage || "/placeholder.svg"}
          alt=""
          fill
          className="object-cover transition-transform duration-500 hover:scale-105"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      </Link>

      <CardHeader className="pb-2">
        <div className="flex items-center gap-2 mb-1">
          <Badge variant="secondary" className="rounded-full">
            {post.category}
          </Badge>
          <span className="text-xs text-muted-foreground">{formatDate(post.date)}</span>
        </div>
        <h2 id={`post-${post.id}-title`} className="text-xl font-bold line-clamp-2">
          <Link href={`/blog/${post.slug}`} className="hover:text-primary transition-colors">
            {post.title}
          </Link>
        </h2>
      </CardHeader>

      <CardContent className="pb-2 flex-grow">
        <p className="text-muted-foreground line-clamp-3">{post.excerpt}</p>
      </CardContent>

      <CardFooter className="pt-2 text-xs text-muted-foreground">{post.readingTime} min read</CardFooter>
    </Card>
  )
}
