"use client";
import Image from "next/image";
import { Download, ChevronLeft, ChevronRight, Loader2 } from "lucide-react";
import { PromptData } from "@/types/astria";
import { useEffect, useState, useTransition } from "react";
import { downloadImage } from "@/utility/utility";
import { usePacks } from "@/context/PacksContext";
import { CustomToolTipWrapper } from "../CustomToolTipWrapper";
import { useRouter, useParams } from "next/navigation";
import { Button } from "../ui/Button";
import { packPaymentCheckoutSessions } from "@/lib/stripe";
import { useUser } from "@/context/UserContext";

interface AstriaPackPageProps {
  initialPromptData: PromptData;
}

export default function AstriaPacksPage({
  initialPromptData,
}: AstriaPackPageProps) {
  const { user } = useUser();
  const promptData = initialPromptData;
  const { packsPrompt, getPacksPrompt } = usePacks();

  const [imageUrl, setImageUrl] = useState(promptData.images?.[0]);

  if (!promptData) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center text-white p-4">
        <p>Prompt data is not available.</p>
      </div>
    );
  }

  if (!imageUrl) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center text-white p-4">
        <p>Error: No image found for this prompt.</p>
      </div>
    );
  }

  const displayPrompt = cleanPromptText(promptData.text);
  const displayModel =
    promptData.tunes?.[0]?.title || "Model info not specified";
  const displayPhotoPack = promptData.tune?.title || "Pack info not specified";
  const displayLicense = "Free to use with backlink to Burstmode AI";
  const displayTakenDate = formatDate(promptData.created_at);

  const relatedPhotosHardcoded = promptData.images || [];

  const [isPending, startTransition] = useTransition();

  const downloadImageUsingUrl = () => {
    startTransition(() => {
      downloadImage(imageUrl);
    });
  };

  const router = useRouter();
  const params = useParams();

  useEffect(() => {
    if (packsPrompt.length === 0 && params.packId) {
      console.log("Fetching prompts");
      getPacksPrompt(params.packId as string);
    }
  }, []);

  const handleImageChange = (action: "prev" | "next") => {
    const promptId = params.promptId;
    const packId = params.packId;
    const promptIdIndex = packsPrompt.findIndex(
      (item) => item.id === Number(promptId)
    );
    let newPromptId = promptId;
    if (action === "next" && promptIdIndex < packsPrompt.length - 1) {
      newPromptId = packsPrompt[promptIdIndex + 1].id.toString();
    } else {
      newPromptId = packsPrompt[0].id.toString();
    }
    if (action === "prev" && promptIdIndex > 0) {
      newPromptId = packsPrompt[promptIdIndex - 1].id.toString();
    } else {
      newPromptId = packsPrompt[packsPrompt.length - 1].id.toString();
    }

    router.push(`/packs/${packId}/${newPromptId}`, { scroll: false });
  };

  const navigateToGenerateImage = async () => {
    if (!user) {
      router.push(`/login?next=/packs/${params.packId}/${params.promptId}`);
      return;
    }
    // router.push(`/packs/${params.packId}/${params.promptId}/generate`)
    packPaymentCheckoutSessions(user?.uid, params.packId as string);
  };

  return (
    <div className="min-h-screen py-28 bg-black text-white flex flex-col items-center px-4 relative">
      <div className="absolute left-4 md:left-8 top-1/2 -translate-y-1/2 z-20 hidden lg:block">
        <CustomToolTipWrapper content="Previous image">
          <button
            aria-label="Previous image"
            className="p-3 bg-black bg-opacity-60 rounded-full hover:bg-opacity-80 transition-colors"
            onClick={() => handleImageChange("prev")}
          >
            <ChevronLeft size={32} />
          </button>
        </CustomToolTipWrapper>
      </div>
      <div className="absolute right-4 md:right-8 top-1/2 -translate-y-1/2 z-20 hidden lg:block">
        <CustomToolTipWrapper content="Next image">
          <button
            aria-label="Next image"
            className="p-3 bg-black bg-opacity-60 rounded-full hover:bg-opacity-80 transition-colors"
            onClick={() => handleImageChange("next")}
          >
            <ChevronRight size={32} />
          </button>
        </CustomToolTipWrapper>
      </div>

      <div className="grid grid-cols-7 max-w-5xl w-full gap-5 justify-items-center m-auto">
        <div className="w-full  relative col-span-4">
          <div className="relative aspect-[3/4] bg-zinc-800 rounded-lg overflow-hidden shadow-2xl">
            <Image
              src={imageUrl}
              alt={displayPrompt || "Generated AI image"}
              fill
              style={{ objectFit: "cover" }}
              priority
            />
            <div className="absolute top-0 left-0 bg-green-400 text-black text-[10px] font-bold px-2.5 py-1 shadow-md">
              BURSTMODE AI GENERATED
            </div>
          </div>
          <a
            href="#"
            className="block text-xs text-zinc-400 hover:text-zinc-200 mt-3 underline text-center lg:text-left"
          >
            Report this photo
          </a>
        </div>

        <div className="col-span-3 max-w-sm grid space-y-5 bg-zinc-800 p-6 rounded-lg shadow-2xl">
          <div>
            <h3 className="text-xs text-zinc-400 uppercase tracking-wider mb-1.5">
              Prompt
            </h3>
            <p className="text-sm text-zinc-100 leading-relaxed max-h-28 overflow-y-auto">
              {displayPrompt}
            </p>
          </div>

          <Button
            onClick={navigateToGenerateImage}
            className="w-full bg-gradient-to-r from-purple-600 via-pink-500 to-teal-500 hover:opacity-90 text-white font-semibold py-3.5 px-4 rounded-lg text-sm flex items-center justify-center transition-all duration-300 ease-in-out transform hover:scale-[1.02]"
          >
            Generate this with yourself in it{" "}
            <span className="ml-2 text-lg">→</span>
          </Button>

          <DetailItem label="Photo Pack" value={displayPhotoPack} />
          <DetailItem label="Model" value={displayModel} />
          <DetailItem label="License" value={displayLicense} />
          <DetailItem label="Taken" value={displayTakenDate} />

          <button
            onClick={downloadImageUsingUrl}
            className="w-full bg-zinc-700 hover:bg-zinc-600 text-zinc-100 font-medium py-3 px-4 rounded-lg text-sm flex items-center justify-center transition-colors cursor-pointer gap-3"
            disabled={isPending}
          >
            {isPending ? (
              <>
                <Loader2 className="animate-spin h-4 w-4 " />
                Downloading...
              </>
            ) : (
              <>
                <Download size={18} className="mr-2.5" />
                Download image
              </>
            )}
          </button>

          <div>
            <h3 className="text-xs text-zinc-400 uppercase tracking-wider mt-5 mb-2.5">
              Related Photos
            </h3>
            <div className="grid grid-cols-3 gap-2.5">
              {relatedPhotosHardcoded.map((src, index) => (
                <div
                  key={index}
                  className="aspect-square bg-zinc-700 rounded overflow-hidden relative"
                >
                  <Image
                    src={src}
                    alt={`Related photo ${index + 1}`}
                    fill
                    style={{ objectFit: "cover" }}
                    className="hover:opacity-80 transition-opacity cursor-pointer"
                    onClick={() => setImageUrl(src)}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

const DetailItem: React.FC<{
  label: string;
  value: string | React.ReactNode;
}> = ({ label, value }) => (
  <div>
    <h3 className="text-xs text-zinc-400 uppercase tracking-wider mb-1">
      {label}
    </h3>
    <p className="text-sm text-zinc-100 break-words">{value}</p>
  </div>
);

function formatDate(dateString: string | undefined): string {
  if (!dateString) return "Date not available";
  try {
    return new Date(dateString).toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  } catch (e) {
    console.error("Error formatting date:", dateString, e);
    return "Invalid date";
  }
}

function cleanPromptText(text: string | undefined): string {
  if (!text) return "Prompt not available";
  return text.replace(/<(faceid|concept):[^>]+>/gi, "").trim();
}
