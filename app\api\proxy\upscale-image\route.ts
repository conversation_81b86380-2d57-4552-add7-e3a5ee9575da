import { NextRequest, NextResponse } from "next/server";
import { createAstriaUpscaleFormData } from "@/lib/astria-form-utils";

export async function POST(request: NextRequest) {
  const defaultModelId = "666678";
  try {
    const formData = await request.formData();
    const prompt = formData.get("prompt") as string;
    const user = formData.get("user") as string;
    const inputImage = formData.get("input_image") as File;
    const superResolution = formData.get("super_resolution") === "true";
    const hiresFixEnabled = formData.get("hires_fix") === "true";
    const hiresCfgScale = formData.get("hires_cfg_scale") as string | null;
    const resemblance = formData.get("resemblance") as string | null;
    const rawModelId = formData.get("model_id") || defaultModelId;
    const modelId = JSON.parse(rawModelId as string) || defaultModelId;

    console.log(formData);
    // Validate required parameters
    if (!user || !inputImage) {
      return NextResponse.json(
        {
          error:
            "Missing required parameters: user and input_image are required",
        },
        { status: 400 }
      );
    }

    // Get denoising_strength from the request or use default 0.1
    const denoisingStrength =
      (formData.get("denoising_strength") as string) || "0.1";

    // Create Astria FormData using centralized utility
    const astriaFormData = createAstriaUpscaleFormData({
      prompt: prompt || "",
      user,
      modelId: modelId.toString(),
      inputImage,
      denoisingStrength,
      superResolution: superResolution ? "true" : undefined,
      hiresFixEnabled: hiresFixEnabled ? "true" : undefined,
      hiresCfgScale,
      resemblance,
      backendVersion: "1",
    });

    console.log("astria formdata", astriaFormData);

    // Call Astria API
    const url = `${process.env.NEXT_PUBLIC_ASTRIA_BASE_URL}/tunes/${modelId}/prompts`;
    const res = await fetch(url, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${process.env.NEXT_PUBLIC_ASTRIA_API_KEY}`,
      },
      body: astriaFormData,
    });

    if (!res.ok) {
      const errorData = await res.json();
      console.error("Astria API Error:", errorData);
      return NextResponse.json(
        { error: res.statusText },
        { status: res.status }
      );
    }

    return NextResponse.json(await res.json());
  } catch (error) {
    console.error("Error in upscale-image POST request:", error);
    return NextResponse.json(
      { error: "An error occurred while processing your request" },
      { status: 500 }
    );
  }
}
