"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { motion } from "framer-motion";
import { CheckCircle, ArrowRight, Download, Sparkles, Gift } from "lucide-react";
import { Button } from "@/components/ui/Button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { cn } from "@/lib/utils";

const PaymentSuccessContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(true);
  const [showConfetti, setShowConfetti] = useState(false);

  // Get pack details from URL params
  const packName = searchParams.get("pack") || "Photo Pack";
  const amount = searchParams.get("amount") || "15.00";

  useEffect(() => {
    // Simulate processing time
    const timer = setTimeout(() => {
      setIsLoading(false);
      setShowConfetti(true);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const handleContinue = () => {
    router.push("/packs");
  };


  useEffect(()=>{
    // onPackPaymentConfirmation
  },[])



  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-background via-background to-secondary/20">
        <motion.div
          className="flex flex-col items-center"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          {/* Processing Animation */}
          <div className="relative mb-8">
            {/* Spinning rings */}
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className={`absolute border-2 rounded-full ${
                  i === 0 ? "w-16 h-16 border-purple-500/30" :
                  i === 1 ? "w-20 h-20 border-blue-500/30" :
                  "w-24 h-24 border-pink-500/30"
                }`}
                style={{
                  left: "50%",
                  top: "50%",
                  marginLeft: i === 0 ? -32 : i === 1 ? -40 : -48,
                  marginTop: i === 0 ? -32 : i === 1 ? -40 : -48,
                }}
                animate={{ rotate: 360 }}
                transition={{
                  duration: 2 + i,
                  repeat: Infinity,
                  ease: "linear",
                }}
              />
            ))}
            
            {/* Center icon */}
            <motion.div
              className="relative z-10 w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center"
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <Gift className="w-6 h-6 text-white" />
            </motion.div>
          </div>

          <motion.p
            className="text-lg font-medium bg-gradient-to-r from-purple-400 via-blue-500 to-purple-400 bg-clip-text text-transparent animate-[gradient-slide_3s_linear_infinite] bg-[length:200%_200%]"
            animate={{ opacity: [0.7, 1, 0.7] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            Processing your purchase...
          </motion.p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-secondary/20 flex items-center justify-center p-4 relative overflow-hidden">
      {/* Background Sparkles */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={`sparkle-${i}`}
            className="absolute"
            initial={{
              x: Math.random() * (typeof window !== 'undefined' ? window.innerWidth : 1000),
              y: Math.random() * (typeof window !== 'undefined' ? window.innerHeight : 1000),
              scale: 0,
            }}
            animate={{
              scale: [0, 1, 0],
              rotate: [0, 180, 360],
            }}
            transition={{
              duration: 3,
              delay: Math.random() * 2,
              repeat: Infinity,
              repeatDelay: Math.random() * 3,
            }}
          >
            <Sparkles className="w-4 h-4 text-purple-400/30" />
          </motion.div>
        ))}
      </div>

      {/* Confetti Effect */}
      {showConfetti && (
        <div className="fixed inset-0 pointer-events-none overflow-hidden z-50">
          {[...Array(50)].map((_, i) => (
            <motion.div
              key={i}
              className={`absolute w-2 h-2 ${
                i % 3 === 0 ? "bg-purple-500" :
                i % 3 === 1 ? "bg-blue-500" : "bg-pink-500"
              } rounded-full`}
              initial={{
                x: typeof window !== 'undefined' ? Math.random() * window.innerWidth : Math.random() * 1000,
                y: -10,
                rotate: 0,
              }}
              animate={{
                y: typeof window !== 'undefined' ? window.innerHeight + 10 : 1000,
                rotate: 360,
                x: typeof window !== 'undefined' ? Math.random() * window.innerWidth : Math.random() * 1000,
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                delay: Math.random() * 2,
                ease: "easeOut",
              }}
            />
          ))}
        </div>
      )}

      <motion.div
        className="w-full max-w-md"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Card className="relative overflow-hidden border-0 bg-card/50 backdrop-blur-sm shadow-2xl">
          {/* Animated gradient border */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-green-500 via-emerald-500 to-green-500 p-[1px] rounded-xl"
            animate={{
              background: [
                "linear-gradient(to right, #10b981, #059669, #10b981)",
                "linear-gradient(to right, #059669, #10b981, #059669)",
                "linear-gradient(to right, #10b981, #059669, #10b981)",
              ]
            }}
            transition={{ duration: 3, repeat: Infinity }}
          >
            <div className="bg-card rounded-xl h-full w-full" />
          </motion.div>

          {/* Floating elements around the card */}
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={`float-${i}`}
              className={`absolute w-2 h-2 rounded-full ${
                i % 2 === 0 ? "bg-green-400/20" : "bg-emerald-400/20"
              }`}
              style={{
                left: `${20 + (i * 15)}%`,
                top: `${10 + (i % 2) * 80}%`,
              }}
              animate={{
                y: [0, -10, 0],
                opacity: [0.2, 0.8, 0.2],
              }}
              transition={{
                duration: 2 + (i * 0.3),
                repeat: Infinity,
                delay: i * 0.2,
              }}
            />
          ))}
          
          <div className="relative z-10">
            <CardHeader className="text-center pb-4">
              {/* Success Icon with Animation */}
              <motion.div
                className="mx-auto mb-4"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ 
                  type: "spring", 
                  stiffness: 200, 
                  damping: 10,
                  delay: 0.3 
                }}
              >
                <div className="relative">
                  {/* Pulsing background */}
                  <motion.div
                    className="absolute inset-0 bg-green-500/20 rounded-full"
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                  <div className="relative w-16 h-16 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-8 h-8 text-white" />
                  </div>
                </div>
              </motion.div>

              {/* Success Message */}
              <motion.h1
                className="text-2xl font-bold text-foreground mb-2"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                Payment Successful!
              </motion.h1>
              
              <motion.p
                className="text-muted-foreground"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
              >
                Thank you for your purchase
              </motion.p>
            </CardHeader>

            <CardContent className="space-y-6">
              {/* Purchase Details */}
              <motion.div
                className="bg-secondary/50 rounded-lg p-4 space-y-2"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.7 }}
              >
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Pack:</span>
                  <span className="font-medium">{packName}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Amount:</span>
                  <span className="font-medium">${amount}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Status:</span>
                  <span className="text-green-500 font-medium flex items-center gap-1">
                    <CheckCircle className="w-4 h-4" />
                    Complete
                  </span>
                </div>
              </motion.div>

              {/* Action Buttons */}
              <motion.div
                className="space-y-3"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
              >
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  
                </motion.div>
                
                <Button
                  onClick={handleContinue}
                  variant="outline"
                  className="w-full"
                  size="lg"
                >
                  Browse More Packs
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </motion.div>

              {/* Additional Info */}
              <motion.div
                className="text-center text-sm text-muted-foreground"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1 }}
              >
                <p>Your pack is ready for download and has been added to your account.</p>
              </motion.div>
            </CardContent>
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default PaymentSuccessContent;
