"use client";

import React, { useState } from "react";
import { Download, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/Button";
import { useToast } from "@/hooks/use-toast";

interface DownloadButtonProps {
  images: string[];
  className?: string;
  variant?: "default" | "outline" | "secondary" | "ghost";
  size?: "default" | "sm" | "lg" | "icon";
  showIndividualButtons?: boolean;
}

const DownloadButton: React.FC<DownloadButtonProps> = ({
  images,
  className,
  variant = "outline",
  size = "sm",
  showIndividualButtons = false,
}) => {
  const [isDownloading, setIsDownloading] = useState(false);
  const { toast } = useToast();

  const downloadSingleImage = async (imageUrl: string, filename: string) => {
    try {
      const response = await fetch(imageUrl);
      if (!response.ok) throw new Error("Failed to fetch image");
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading image:", error);
      throw error;
    }
  };

  const downloadMultipleImages = async (imageUrls: string[]) => {
    try {
      // Dynamic import of JSZip to reduce bundle size
      const JSZip = (await import("jszip")).default;
      const zip = new JSZip();

      // Download all images and add to zip
      const downloadPromises = imageUrls.map(async (imageUrl, index) => {
        try {
          const response = await fetch(imageUrl);
          if (!response.ok) throw new Error(`Failed to fetch image ${index + 1}`);
          
          const blob = await response.blob();
          const extension = getImageExtension(imageUrl) || "jpg";
          const filename = `burst-mode-image-${index + 1}.${extension}`;
          zip.file(filename, blob);
        } catch (error) {
          console.error(`Error downloading image ${index + 1}:`, error);
          // Continue with other images even if one fails
        }
      });

      await Promise.all(downloadPromises);

      // Generate and download zip file
      const zipBlob = await zip.generateAsync({ type: "blob" });
      const url = window.URL.createObjectURL(zipBlob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `burst-mode-images-${new Date().toISOString().split('T')[0]}.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error creating zip file:", error);
      throw error;
    }
  };

  const getImageExtension = (url: string): string => {
    try {
      const pathname = new URL(url).pathname;
      const extension = pathname.split('.').pop()?.toLowerCase();
      return ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension || '') 
        ? extension || 'jpg' 
        : 'jpg';
    } catch {
      return 'jpg';
    }
  };

  const handleDownload = async () => {
    if (images.length === 0) {
      toast({
        title: "No images to download",
        description: "Please generate some images first.",
        variant: "destructive",
      });
      return;
    }

    setIsDownloading(true);

    try {
      if (images.length === 1) {
        // Single image download
        const extension = getImageExtension(images[0]);
        const filename = `burst-mode-image.${extension}`;
        await downloadSingleImage(images[0], filename);
        
        toast({
          title: "Download complete",
          description: "Image downloaded successfully.",
        });
      } else {
        // Multiple images download as ZIP
        await downloadMultipleImages(images);
        
        toast({
          title: "Download complete",
          description: `${images.length} images downloaded as ZIP file.`,
        });
      }
    } catch (error) {
      console.error("Download failed:", error);
      toast({
        title: "Download failed",
        description: "There was an error downloading the images. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDownloading(false);
    }
  };

  if (images.length === 0) {
    return null;
  }

  return (
    <Button
      onClick={handleDownload}
      disabled={isDownloading}
      variant={variant}
      size={size}
      className={className}
    >
      {isDownloading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <Download className="h-4 w-4" />
      )}
      {size !== "icon" && (
        <span className="ml-2">
          {isDownloading 
            ? "Downloading..." 
            : images.length === 1 
              ? "Download" 
              : `Download (${images.length})`
          }
        </span>
      )}
    </Button>
  );
};

// Individual download button for single images
interface IndividualDownloadButtonProps {
  imageUrl: string;
  index: number;
  className?: string;
}

export const IndividualDownloadButton: React.FC<IndividualDownloadButtonProps> = ({
  imageUrl,
  index,
  className,
}) => {
  const [isDownloading, setIsDownloading] = useState(false);
  const { toast } = useToast();

  const getImageExtension = (url: string): string => {
    try {
      const pathname = new URL(url).pathname;
      const extension = pathname.split('.').pop()?.toLowerCase();
      return ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension || '')
        ? extension || 'jpg'
        : 'jpg';
    } catch {
      return 'jpg';
    }
  };

  const handleDownload = async () => {
    setIsDownloading(true);

    try {
      const response = await fetch(imageUrl);
      if (!response.ok) throw new Error("Failed to fetch image");

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const extension = getImageExtension(imageUrl);
      const filename = `burst-mode-image-${index + 1}.${extension}`;

      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Download complete",
        description: `Image ${index + 1} downloaded successfully.`,
      });
    } catch (error) {
      console.error("Download failed:", error);
      toast({
        title: "Download failed",
        description: "There was an error downloading the image. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <Button
      onClick={handleDownload}
      disabled={isDownloading}
      variant="secondary"
      size="icon"
      className={`absolute top-2 right-2 z-10 bg-black/50 hover:bg-black/70 text-white border-none ${className}`}
    >
      {isDownloading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <Download className="h-4 w-4" />
      )}
    </Button>
  );
};

export default DownloadButton;
