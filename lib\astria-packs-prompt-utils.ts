export interface AstriaPromptParams {
  text: string;
  modelId?: string;
  numImages: string;
  image?: File;
  guidanceScale: string;
  userId?: string;
}

export const createAstriaPackPromptFormData = (
  params: AstriaPromptParams
): FormData => {
  const formData = new FormData();

  let prompt: string = params.text;

  if (params.modelId) {
    prompt = `<lora:${params.modelId}:1.0> ${prompt}`;
  }

  formData.append("prompt[text]", prompt);
  formData.append("prompt[super_resolution]", "true");
  formData.append("prompt[face_correct]", "true");
  formData.append("prompt[num_images]", params.numImages);
  formData.append("prompt[face_swap]", "true");
  
  if (params.image) {
    formData.append("prompt[input_image]", params.image);
    formData.append("controlnet_txt2img", "false");
    formData.append("denoising_strength", params.guidanceScale);
  } else {
    formData.append("controlnet_txt2img", "true");
  }

  const callbackUrl = `${process.env.NEXT_PUBLIC_APP_URL}/api/callback/packs-prompt?userId=${params.userId}`;
  formData.append("prompt[callback]", callbackUrl);

  return formData;
};