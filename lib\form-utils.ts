// lib/form-utils.ts
// Centralized form handling utilities to eliminate FormData duplication

export interface BaseFormParams {
  user: string;
  prompt?: string;
  enhancementType?: string;
}

export interface ImageGenerationParams extends BaseFormParams {
  tuneId: string;
  inputImage?: File | null;
  controlnet?: string | null;
  denoisingStrength?: number;
  style?: string;
  garmentModelId?: string;
  controlnets?: string[];
  controlnetWeights?: number[];
  maskPrompt?: string;
  maskInvert?: boolean;
  superResolution?: boolean;
  hiresFixEnabled?: boolean;
  tiledUpscale?: boolean;
  tiledUpscaleVersion?: "v1" | "v2";
  hiresCfgScale?: number;
  resemblance?: number;
}

export interface VideoGenerationParams extends BaseFormParams {
  tuneId: string;
  videoPrompt?: string;
  videoModel?: "720p" | "480p";
  inputImage?: File | null;
  controlnet?: string | null;
  denoisingStrength?: number;
  style?: string;
}

export interface ModelCreationParams extends BaseFormParams {
  title: string;
  images: File[];
  modelType?: "lora" | "faceid";
  className?: string;
}

export interface AvatarGenerationParams extends BaseFormParams {
  inputImage?: File | null;
  controlnet?: string | null;
  denoisingStrength?: string | null;
  style?: string | null;
}

export interface UpscaleParams extends BaseFormParams {
  inputImage: File;
  superResolution?: boolean;
  hiresFixEnabled?: boolean;
  hiresCfgScale?: string | null;
  resemblance?: string | null;
  modelId?: string;
  denoisingStrength?: number;
}

/**
 * Creates FormData for image generation requests
 * Centralizes the FormData creation logic used across multiple components
 */
export function createImageGenerationFormData(params: ImageGenerationParams): FormData {
  const formData = new FormData();

  // Required fields
  formData.append("user", params.user);

  if (params.prompt) {
    formData.append("prompt", params.prompt);
  }

  if (params.enhancementType) {
    formData.append("enhancement_type", params.enhancementType);
  }

  // Optional image upload
  if (params.inputImage) {
    formData.append("input_image", params.inputImage);
  }

  // ControlNet parameters
  if (params.controlnet) {
    formData.append("controlnet", params.controlnet);
  }

  if (params.denoisingStrength !== undefined) {
    formData.append("denoising_strength", params.denoisingStrength.toString());
  }

  // Style parameter
  if (params.style) {
    formData.append("style", params.style);
  }

  // Virtual try-on specific
  if (params.garmentModelId) {
    formData.append("garment_model_id", params.garmentModelId);
  }

  // Interior design specific
  if (params.controlnets && params.controlnets.length > 0) {
    params.controlnets.forEach((controlnet, index) => {
      formData.append(`controlnets[${index}]`, controlnet);
    });
  }

  if (params.controlnetWeights && params.controlnetWeights.length > 0) {
    params.controlnetWeights.forEach((weight, index) => {
      formData.append(`controlnet_weights[${index}]`, weight.toString());
    });
  }

  if (params.maskPrompt) {
    formData.append("mask_prompt", params.maskPrompt);
  }

  if (params.maskInvert !== undefined) {
    formData.append("mask_invert", params.maskInvert.toString());
  }

  // Upscaling parameters
  if (params.superResolution !== undefined) {
    formData.append("super_resolution", params.superResolution.toString());
  }

  if (params.hiresFixEnabled !== undefined) {
    formData.append("hires_fix", params.hiresFixEnabled.toString());
  }

  if (params.tiledUpscale !== undefined) {
    formData.append("tiled_upscale", params.tiledUpscale.toString());
  }

  if (params.tiledUpscaleVersion) {
    formData.append("tiled_upscale_version", params.tiledUpscaleVersion);
  }

  if (params.hiresCfgScale !== undefined) {
    formData.append("hires_cfg_scale", params.hiresCfgScale.toString());
  }

  if (params.resemblance !== undefined) {
    formData.append("resemblance", params.resemblance.toString());
  }

  return formData;
}

/**
 * Creates FormData for video generation requests
 */
export function createVideoGenerationFormData(params: VideoGenerationParams): FormData {
  const formData = new FormData();

  // Required fields
  formData.append("user", params.user);
  formData.append("tune_id", params.tuneId);

  if (params.prompt) {
    formData.append("prompt", params.prompt);
  }

  // Video-specific parameters
  if (params.videoPrompt) {
    formData.append("video_prompt", params.videoPrompt);
  }

  if (params.videoModel) {
    formData.append("video_model", params.videoModel);
  }

  // Optional image upload
  if (params.inputImage) {
    formData.append("input_image", params.inputImage);
  }

  // ControlNet parameters
  if (params.controlnet) {
    formData.append("controlnet", params.controlnet);
  }

  if (params.denoisingStrength !== undefined) {
    formData.append("denoising_strength", params.denoisingStrength.toString());
  }

  // Style parameter
  if (params.style) {
    formData.append("style", params.style);
  }

  return formData;
}

/**
 * Creates FormData for model creation/training requests
 */
export function createModelCreationFormData(params: ModelCreationParams): FormData {
  const formData = new FormData();

  // Required fields
  formData.append("user", params.user);
  formData.append("title", params.title);
  formData.append("model_type", params.modelType || "lora");

  if (params.enhancementType) {
    formData.append("enhancementType", params.enhancementType);
  }

  // Add className for faceid models
  if (params.modelType === "faceid" && params.className) {
    formData.append("className", params.className);
  }

  // Add images
  params.images.forEach((image) => {
    formData.append("images", image);
  });

  return formData;
}

/**
 * Creates FormData for avatar generation requests
 */
export function createAvatarGenerationFormData(params: AvatarGenerationParams): FormData {
  const formData = new FormData();

  // Required fields
  formData.append("user", params.user);

  if (params.prompt) {
    formData.append("prompt", params.prompt);
  }

  // Optional image upload
  if (params.inputImage) {
    formData.append("input_image", params.inputImage);
  }

  // ControlNet parameters
  if (params.controlnet) {
    formData.append("controlnet", params.controlnet);
  }

  if (params.denoisingStrength) {
    formData.append("denoising_strength", params.denoisingStrength);
  }

  // Style parameter
  if (params.style) {
    formData.append("style", params.style);
  }

  return formData;
}

/**
 * Creates FormData for upscaling requests
 */
export function createUpscaleFormData(params: UpscaleParams): FormData {
  const formData = new FormData();

  // Required fields
  formData.append("user", params.user);
  formData.append("input_image", params.inputImage);

  if (params.prompt) {
    formData.append("prompt", params.prompt);
  }

  if (params.denoisingStrength !== undefined) {
    formData.append("denoising_strength", params.denoisingStrength.toString());
  }

  if (params.modelId) {
    formData.append("model_id", JSON.stringify(params.modelId));
  }

  // Upscaling parameters
  if (params.superResolution !== undefined) {
    formData.append("super_resolution", params.superResolution.toString());
  }

  if (params.hiresFixEnabled !== undefined) {
    formData.append("hires_fix", params.hiresFixEnabled.toString());
  }

  if (params.hiresCfgScale) {
    formData.append("hires_cfg_scale", params.hiresCfgScale);
  }

  if (params.resemblance) {
    formData.append("resemblance", params.resemblance);
  }

  return formData;
}
