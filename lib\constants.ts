export const PRODUCT_SECTIONS = [
  {
    id: 6,
    title: "Professional Headshots",
    description: "Transform your ordinary selfies into stunning professional headshots. Perfect for LinkedIn profiles, business cards, and professional portfolios.",
    testimonial: {
      quote: "The headshot enhancement feature helped me stand out on LinkedIn. I received more connection requests and job opportunities after updating my profile.",
      author: "<PERSON> Adler",
      role: "Professional",
      avatar: "./_avatars/1.jpg"
    },
    image: "/headshot_result.jpg",
    route: "/model/headshots",
    reversed: true
  },
  {
    id: 1,
    title: "E-commerce Stores",
    description: "Are you tired of generic, uninspiring product shots? Watch how Burst Mode can take a simple photo and transform it into a persuasive, high-converting masterpiece.",
    testimonial: {
      quote: "Using Burst Mode, our online store saw a 25% increase in click-through rates and a 15% boost in sales. The investment paid for itself within the first week!",
      author: "Bryan Derby",
      role: "Store Owner",
      avatar: "./_avatars/2.jpg"
    },
    image: "./product_1.png",
    route: "/model/products",
    reversed: false
  },
  {
    id: 2,
    title: "Restaurants",
    description: "Make every dish look like a culinary work of art. Burst Mode brings out the vibrant colors and textures that make people want to dine in.",
    testimonial: {
      quote: "The quality of our menu photos improved drastically. We saw an immediate 30% increase in online orders after implementing Burst Mode.",
      author: "Monica Jane",
      role: "Store Owner",
      avatar: "./_avatars/3.jpg"
    },
    image: "./product_3.jpg",
    route: "/model/foods",
    reversed: true
  },
  {
    id: 3,
    title: "Social Media Agencies",
    description: "Elevate your clients' brand stories with visuals that pop. Create content that's not just scroll-stopping but also engagement-driving.",
    testimonial: {
      quote: "Since we started using Burst Mode, our clients have seen up to a 50% increase in social media engagement. It's a game-changer.",
      author: "Rachel Smith",
      role: "Agency Owner",

      avatar: "./_avatars/4.jpg"
    },
    image: "./product_2.png",
    route: "/model/products",
    reversed: false
  },
  {
    id: 4,
    title: "Influencers",
    description: "Turn your posts into must-see content. Whether it's fashion, lifestyle, or travel, Burst Mode ensures your photos are as influential as you are.",
    testimonial: {
      quote: "My engagement rates have soared, and brands are now approaching me for partnerships. Burst Mode has been instrumental in my success.",
      author: "Suzanne Green",
      role: "Store Owner",
      avatar: "./_avatars/5.jpg"
    },
    image: "./12.jpg",
    route: "/model/products",
    reversed: true
  },
  {
    id: 5,
    title: "Small Business Owners",
    description: "Show the world what makes your business unique. From product shots to promotional material, get visuals that resonate with your audience.",
    testimonial: {
      quote: "Foot traffic to my store increased significantly once we updated our marketing materials with BurstModeAI-enhanced images.",
      author: "Elizabeth Sullivan",
      role: "Store Owner",
      avatar: "./_avatars/6.jpg"
    },
    image: "./product_5.jpg",
    route: "/model/products",
    reversed: false
  }
];

export const IMAGES = [
  {
    title: "",
    link: "/",
    thumbnail: "/_gallery/4.jpg",
  },
  {
    title: "",
    link: "/",
    thumbnail: "/_gallery/5.jpg",
  },
  {
    title: "",
    link: "/",
    thumbnail: "/_gallery/6.jpg",
  },
  {
    title: "",
    link: "/",
    thumbnail: "/_gallery/7.jpg",
  },
  {
    title: "",
    link: "/",
    thumbnail: "/_gallery/8.jpg",
  },
  {
    title: "",
    link: "/",
    thumbnail: "/_gallery/9.jpg",
  },
  {
    title: "",
    link: "/",
    thumbnail: "/_gallery/10.jpg",
  },
  {
    title: "",
    link: "/",
    thumbnail: "/_gallery/11.jpg",
  },
  {
    title: "",
    link: "/",
    thumbnail: "/_gallery/12.jpg",
  },
  {
    title: "",
    link: "/",
    thumbnail: "/_gallery/13.jpg",
  },
  {
    title: "",
    link: "/",
    thumbnail: "/_gallery/14.jpg",
  },
  {
    title: "",
    link: "/",
    thumbnail: "/_gallery/15.jpg",
  },
  {
    title: "",
    link: "/",
    thumbnail: "/_gallery/16.jpg",
  },
  // Continue pattern up to 65
  ...Array.from({ length: 50 }, (_, i) => ({
    title: "",
    link: "/",
    thumbnail: `/_gallery/${i + 17}.jpg`,
  }))
];

export const PREDEFINED_PROMPTS = {
  headshot: [
    "Professional portrait with soft lighting in an office setting",
    "Casual headshot with natural outdoor lighting and warm tones",
    "Creative artistic portrait with dramatic side lighting"
  ],
  food: [
    "Gourmet dish on a marble tabletop with professional food styling",
    "Rustic food photography with natural ingredients and warm lighting",
    "Modern minimalist food presentation with vibrant colors"
  ],
  product: [
    "Clean product shot on white background with soft shadows",
    "Lifestyle product photography in a natural setting",
    "Creative product placement with dramatic lighting and strong contrast"
  ],
  avatar: [
    "Stylized digital avatar with vibrant colors and modern design",
    "Fantasy character avatar with magical elements and glowing effects",
    "Professional 3D avatar with realistic features and subtle lighting",
    "Anime-style avatar with expressive features and bold colors",
    "Minimalist avatar with clean lines and simple color palette"
  ],
  "virtual try-on": [
    "Person wearing garment in natural outdoor lighting, full body shot",
    "Fashion photoshoot with professional studio lighting, high-end magazine style",
    "Casual street style photography with urban background, trendy look",
    "Person modeling garment on runway, fashion show lighting",
    "Elegant portrait wearing garment, professional fashion photography"
  ],
  interior: [
    "Clean scandinavian design living room, natural light, architecture magazine cover",
    "Modern minimalist kitchen with marble countertops and high-end appliances",
    "Cozy rustic bedroom with warm lighting and wooden accents",
    "Industrial style office space with exposed brick and metal fixtures",
    "Luxury bathroom with freestanding tub and elegant fixtures"
  ],
  upscaling: [
    "High-resolution enhancement with improved details",
    "Super-resolution upscaling with preserved textures",
    "Tiled upscale with enhanced skin tones and realistic details",
    "High-quality upscaling for print-ready images",
    "Enhanced resolution with improved clarity and sharpness"
  ],
  "image-to-video": [
    "Person standing in a beautiful garden with flowers blooming around",
    "Scenic mountain landscape with dramatic clouds and sunlight",
    "Urban cityscape with modern architecture and busy streets",
    "Serene beach scene with waves gently washing ashore",
    "Portrait of a person in a professional studio setting"
  ]
};

export const PLANS = [
  {
    id: 0,
    name: "Starter",
    price: "15",
    yearlyPriceInMonths: "12",
    yearlyPrice: "144",
    yearlyPriceId: process.env.NEXT_PUBLIC_STARTER_YEARLY_PLAN_TEST ?? "",
    priceId: process.env.NEXT_PUBLIC_STARTER_PLAN_TEST ?? "",
    description:
      "Perfect for individuals looking to enhance their online presence.",
    credit: "1",
    imageCredits: "200",
    feature1: "Headshot Generation",
    feature2: "Enhance Food Ads",
    feature3: "Enhance Product Ads",
    feature4: "Low Quality",
    feature5: "Slow Processing",
  },
  {
    id: 1,
    name: "Pro",
    price: "45",
    yearlyPriceInMonths: "36",
    yearlyPrice: "432",
    yearlyPriceId: process.env.NEXT_PUBLIC_PRO_YEARLY_PLAN_TEST ?? "",
    priceId: process.env.NEXT_PUBLIC_PRO_PLAN_TEST ?? "",
    description:
      "Ideal for professionals requiring frequent updates to their profiles.",
    credit: "3",
    imageCredits: "700",
    feature1: "Headshot Generation",
    feature2: "Enhance Food Ads",
    feature3: "Enhance Product Ads",
    feature4: "🌟 High Quality",
    feature5: "⚡ Fast Processing",
  },
  {
    id: 2,
    name: "Premium",
    price: "95",
    yearlyPriceInMonths: "75",
    yearlyPrice: "900",
    yearlyPriceId: process.env.NEXT_PUBLIC_PREMIUM_YEARLY_PLAN_TEST ?? "",
    priceId: process.env.NEXT_PUBLIC_PREMIUM_PLAN_TEST ?? "",
    description: "The best value with unlimited possibilities.",
    credit: "7",
    imageCredits: "1500",
    feature1: "Headshot Generation",
    feature2: "Enhance Food Ads",
    feature3: "Enhance Product Ads",
    feature4: "💎 Best Quality",
    feature5: "⚡ Best Processing",
  },
  {
    id: 3,
    name: "Enterprise",
    price: "195",
    yearlyPriceInMonths: "156",
    yearlyPrice: "1872",
    yearlyPriceId: process.env.NEXT_PUBLIC_ENTERPRISE_YEARLY_PLAN_TEST ?? "",
    priceId: process.env.NEXT_PUBLIC_ENTERPRISE_PLAN_TEST ?? "",
    description: "The best value with unlimited possibilities.",
    credit: "30",
    imageCredits: "4000",
    feature1: "Headshot Generation",
    feature2: "Enhance Food Ads",
    feature3: "Enhance Product Ads",
    feature4: "💎 Best Quality",
    feature5: "⚡ Best Processing",
  },
];


export const modelCards = [
    {
      title: "Launch your Photoshoot",
      imageSrc: "/toolsGallery/1.jpg",
      imageAlt: "Photoshoot",
      href: "/model/headshots",
    },
    {
      title: "Enhance your Food Photography",
      imageSrc: "/enhanced_food.jpg",
      imageAlt: "Food Ad",
      href: "/model/foods",
    },
    {
      title: "Enhance your Product Shots",
      imageSrc: "/product_1.png",
      imageAlt: "Product Ad",
      href: "/model/products",
    },
    {
      title: "Virtual Try-On",
      imageSrc: "/virtual-try-on.png",
      imageAlt: "Virtual Try-On",
      href: "/model/virtual-try-on",
    },
    {
      title: "Interior Design",
      imageSrc: "/interior-design.png",
      imageAlt: "Interior Design",
      href: "/model/interior",
    },
    {
      title: "Image Upscaling",
      imageSrc: "/upscaling.JPG",
      imageAlt: "Image Upscaling",
      href: "/model/upscaling",
    },
    {
      title: "Image to Video",
      imageSrc: "/img2video.mp4",
      imageAlt: "Image to Video",
      href: "/model/image-to-video",
    },
    {
      title: "Generate AI Avatars",
      imageSrc: "/avatar.png",
      imageAlt: "AI Avatars",
      href: "/model/avatar",
    },
  ];