import React, { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { UpscalingOptions } from "./image-prompt/UpscalingOptions";
import UpscalingImageUploader from "./image-prompt/UpscalingImageUploader";
import UpscalingOptionsComponent from "./image-prompt/UpscalingOptions";
import { upscaleImage } from "@/lib/api";
import { ModelSelector } from "@/components/modelTraining";
import { User } from "firebase/auth";
import { Brain } from "lucide-react";
import { CustomToolTipWrapper } from "../CustomToolTipWrapper";
import { Button } from "../ui/Button";
import { downloadImage } from "@/utility/utility";
import { db } from "@/lib/firebase";
import { collection, getDocs, doc, updateDoc } from "firebase/firestore";
import { useUser } from "@/context/UserContext";

interface UpscalingPromptProps {
  userId: string;
  imageUrl: string | null;
  setImageUrl: React.Dispatch<React.SetStateAction<string | null>>;
  onStartProcessing: () => void;
  onCompleteProcessing: () => void;
  user?: User; // Firebase user object for model training
}

const UpscalingPrompt: React.FC<UpscalingPromptProps> = ({
  userId,
  imageUrl,
  setImageUrl,
  onStartProcessing,
  onCompleteProcessing,
  user,
}) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [inputImage, setInputImage] = useState<File | null>(null);
  const [inputImageUrl, setInputImageUrl] = useState<string | null>(null);
  const { decrementImageCredits } = useUser();

  const [upscalingOptions, setUpscalingOptions] = useState<UpscalingOptions>({
    superResolution: true,
    hiresFixEnabled: false,
    tiledUpscale: true,
    tiledUpscaleVersion: "v2",
    hiresCfgScale: 5.5,
    resemblance: 0.65,
    denoisingStrength: 0.1,
    useModelTraining: false,
    modelId: null,
    focusPrompt: "",
  });

  // State to track if model selection UI is shown
  const [showModelSelection, setShowModelSelection] = useState(false);

  // Handle model selection from ModelSelector
  const handleModelSelected = (modelId: string, modelTitle: string) => {
    setUpscalingOptions((prev) => ({
      ...prev,
      modelId,
      useModelTraining: true,
    }));
    setShowModelSelection(false); // Hide model selection UI after model is selected
    toast({
      title: "Model Selected",
      description: `Using model: ${modelTitle}`,
    });
  };

  // Handlers for model training
  const handleTrainingStarted = () => {
    onStartProcessing();
  };

  const handleTrainingFailed = () => {
    toast({
      title: "Model Training Failed",
      description: "There was an error training your model. Please try again.",
      variant: "destructive",
    });
    onCompleteProcessing();
  };

  const handleUpscale = async () => {
    if (!inputImage) {
      toast({
        title: "No image selected",
        description: "Please upload an image to upscale.",
        variant: "destructive",
      });
      return;
    }

    try {
      const subscribedPlanRef = collection(
        db,
        "users",
        userId,
        "subscribedPlan"
      );
      const querySnapshot = await getDocs(subscribedPlanRef);

      if (querySnapshot.empty) {
        setIsLoading(false);
        onCompleteProcessing(); // Stop the processing UI
        toast({
          title: "⚠️ Subscription Issue",
          description:
            "No active subscription plan found. Please check your account settings.",
          variant: "destructive",
          duration: 5000,
        });
        return;
      }

      // Get the first document ID in `subscribedPlan`
      const planDoc = querySnapshot.docs[0];
      const planDocId = planDoc.id;
      const imageCredits = planDoc.data().planImageCredits;

      // Reference the document and decrement imageCredits
      const planRef = doc(db, "users", userId, "subscribedPlan", planDocId);
      await updateDoc(planRef, {
        planImageCredits: imageCredits - 1,
      });

      // Update the local state
      decrementImageCredits();
    } catch (error) {}

    try {
      setIsLoading(true);
      onStartProcessing();

      // Create FormData for the image
      const formData = new FormData();
      formData.append("image", inputImage);

      // Process the upscaling request
      const result = await upscaleImage({
        user: userId,
        inputImage: formData,
        options: upscalingOptions,
      });

      if (result.success && result.data) {
        setImageUrl(result.data[0]);
        toast({
          title: "Image upscaled successfully",
          description: "Your image has been upscaled and enhanced.",
        });
      } else {
        toast({
          title: "Upscaling failed",
          description: result.message || "An error occurred during upscaling.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error upscaling image:", error);
      toast({
        title: "Upscaling failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      onCompleteProcessing();
    }
  };

  return (
    <div className="relative">
      {/* Removed top-right button */}

      {showModelSelection && user ? (
        <div className=" mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Train or Select a Model</h2>
            <Button
              variant={"destructive"}
              onClick={() => setShowModelSelection(false)}
              className=""
            >
              Cancel
            </Button>
          </div>
          <p className="text-white/70 text-sm ">
            Training a custom model will improve upscaling results for your
            specific type of images.
          </p>
          <ModelSelector
            user={user}
            enhancementType="upscaling"
            onModelSelected={handleModelSelected}
            onTrainingStarted={handleTrainingStarted}
            onTrainingFailed={handleTrainingFailed}
          />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-6">
            <div className="bg-black/30 p-2 sm:p-6 rounded-lg border border-white/10">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Upscale Your Image</h2>
                {user && (
                  <CustomToolTipWrapper
                    content={
                      upscalingOptions.modelId
                        ? "Change Model"
                        : "Train Your Model"
                    }
                  >
                    <button
                      onClick={() => setShowModelSelection(true)}
                      className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg flex items-center gap-2 text-sm font-medium transition-all duration-200"
                    >
                      <Brain size={16} />
                    </button>
                  </CustomToolTipWrapper>
                )}
              </div>
              {upscalingOptions.modelId && (
                <div className="mb-4 bg-purple-900/30 p-2 rounded-md border border-purple-500/30 text-sm text-white/80">
                  Using trained model for better results
                </div>
              )}
              <p className="text-white/70 text-sm mb-6">
                Upload an image to enhance its resolution and quality. Our AI
                will upscale your image while preserving details and improving
                clarity.
              </p>

              <UpscalingImageUploader
                inputImage={inputImage}
                setInputImage={setInputImage}
                inputImageUrl={inputImageUrl}
                setInputImageUrl={setInputImageUrl}
              />
            </div>

            <UpscalingOptionsComponent
              options={upscalingOptions}
              setOptions={setUpscalingOptions}
            />

            <button
              onClick={handleUpscale}
              disabled={isLoading || !inputImage}
              className={`
                w-full
                py-3
                rounded-lg
                font-medium
                transition-all
                flex
                items-center
                justify-center
                gap-2
                ${
                  isLoading || !inputImage
                    ? "bg-purple-500/20 text-white/40 cursor-not-allowed"
                    : "bg-purple-600 hover:bg-purple-700 text-white"
                }
              `}
            >
              {isLoading ? (
                <>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                  <span>Processing...</span>
                </>
              ) : (
                "Upscale Image"
              )}
            </button>
          </div>

          <div className="bg-black/30 p-6 rounded-lg border border-white/10 flex flex-col items-center justify-center">
            {isLoading ? (
              <div className="flex flex-col items-center space-y-4">
                <div className="relative">
                  <div className="animate-spin rounded-full h-16 w-16 border-2 border-purple-500/20"></div>
                  <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-l-2 border-purple-500 absolute top-0 left-0"></div>
                </div>
                <p className="text-white/70">Upscaling your image...</p>
              </div>
            ) : imageUrl ? (
              <div className="flex flex-col items-center">
                <div className="overflow-hidden rounded-lg shadow-2xl border border-white/10">
                  <img
                    src={imageUrl}
                    alt="Upscaled"
                    className="w-auto max-h-80 object-contain"
                  />
                </div>

                <button
                  onClick={() => downloadImage(imageUrl)}
                  className="
                            mt-6
                            bg-black/60
                            hover:bg-black/80
                            text-white
                            font-medium
                            py-2 px-5
                            rounded-lg
                            transition-all
                            duration-200
                            shadow-lg
                            flex items-center gap-2
                            border border-white/10
                          "
                >
                  Download
                </button>
              </div>
            ) : (
              <div className="text-center space-y-3 max-w-xs">
                <div className="text-white text-lg opacity-60">
                  Your upscaled image
                </div>
                <div className="text-white text-sm opacity-40">
                  Upload an image and adjust settings to generate an enhanced
                  version
                </div>
                <div className="mt-4 text-purple-400/60 text-4xl">✨</div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default UpscalingPrompt;
